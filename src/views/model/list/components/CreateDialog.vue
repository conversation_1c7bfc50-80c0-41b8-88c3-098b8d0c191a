<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加模型"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="模型名称" prop="modelName">
        <el-input
          v-model="formData.modelName"
          placeholder="请输入模型名称"
          clearable
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="告警类型" prop="warningType">
        <el-select
          v-model="formData.warningType"
          placeholder="请选择告警类型"
          clearable
          style="width: 100%"
        >
          <el-option label="反光背心" value="reflective_vest" />
          <el-option label="安全绳" value="safety_rope" />
        </el-select>
      </el-form-item>

      <el-form-item label="上传封面图" prop="coverImageFileId">
        <UploadImgDetail
          v-model="coverUrl"
          :file-size="5"
          :file-type="['image/jpeg', 'image/png']"
          width="120px"
          height="120px"
          :upload-api="handleUpload"
          @upload-success="handleUploadSuccess"
        />
      </el-form-item>

      <el-form-item label="上传模型" prop="modelFileId">
        <el-upload
          ref="modelUploadRef"
          :auto-upload="false"
          :on-change="handleModelFileChange"
          :before-upload="beforeModelUpload"
          :show-file-list="true"
          :limit="1"
          accept=".zip,.rar,.tar,.gz,.7z,.model,.pkl,.pt,.pth,.onnx"
        >
          <el-button type="primary">
            <el-icon><Upload /></el-icon>
            选择模型文件
          </el-button>
          <template #tip>
            <div class="upload-tip"
              >请上传模型文件（支持 .zip, .rar, .model, .pkl, .pt, .pth, .onnx 等格式）</div
            >
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="fillWithTestData">快速填充</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { UploadImgDetail } from '@/components/UploadFile'
import { Upload } from '@element-plus/icons-vue'
import { createModel } from '@/api/alg/model'
import { uploadFileDetail } from '@/api/infra/file'
import type { CreateModelForm } from '../types'

defineOptions({ name: 'CreateModelDialog' })

const emit = defineEmits<{
  success: []
}>()

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const modelUploadRef = ref()

const formData = reactive<CreateModelForm>({
  modelName: '',
  modelFileId: '',
  coverImageFileId: undefined,
  modelVideoFileId: undefined,
  status: undefined,
  warningType: undefined
})
const coverUrl = ref('')
let coverImageFileId: number | undefined

const formRules: FormRules<CreateModelForm> = {
  modelName: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 1, max: 50, message: '模型名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  modelFileId: [{ required: true, message: '请上传模型文件', trigger: 'blur' }],
  warningType: [{ required: true, message: '请选择告警类型', trigger: 'change' }],
  coverImageFileId: [{ required: true, message: '请上传封面图', trigger: 'change' }]
}

/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true
  resetForm()
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  Object.assign(formData, {
    modelName: '',
    modelFileId: '',
    coverImageFileId: undefined,
    modelVideoFileId: undefined,
    status: undefined,
    warningType: undefined
  })
  coverUrl.value = ''
  coverImageFileId = undefined
  // 清理上传组件的文件列表
  if (modelUploadRef.value) {
    modelUploadRef.value.clearFiles()
  }
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  const isValid = await formRef.value.validate().catch(() => false)
  if (!isValid) return

  loading.value = true
  try {
    const payload = {
      ...formData,
      ...(coverImageFileId && { coverImageFileId })
    }

    await createModel(payload)
    ElMessage.success('创建成功')
    handleClose()
    emit('success')
  } catch (error) {
    ElMessage.error('创建失败')
    console.error('创建模型失败:', error)
  } finally {
    loading.value = false
  }
}

const handleUpload = async (file: File) => {
  const res = await uploadFileDetail({ file })
  ElMessage.success('上传成功')
  return res
}

const handleUploadSuccess = (res: any) => {
  coverUrl.value = res.data.url
  coverImageFileId = res.data.id
  formData.coverImageFileId = res.data.id // 同时更新表单数据
}

/** 模型文件上传前检查 */
const beforeModelUpload = (file: File) => {
  const isValidType =
    [
      'application/zip',
      'application/x-rar-compressed',
      'application/x-tar',
      'application/gzip',
      'application/x-7z-compressed',
      'application/octet-stream'
    ].includes(file.type) ||
    ['.zip', '.rar', '.tar', '.gz', '.7z', '.model', '.pkl', '.pt', '.pth', '.onnx'].some((ext) =>
      file.name.toLowerCase().endsWith(ext)
    )

  const isLt100M = file.size / 1024 / 1024 < 100

  if (!isValidType) {
    ElMessage.error('请上传正确格式的模型文件！')
    return false
  }
  if (!isLt100M) {
    ElMessage.error('模型文件大小不能超过 100MB！')
    return false
  }
  return true
}

/** 模型文件选择变化处理 */
const handleModelFileChange = async (file: any) => {
  if (file.status === 'ready') {
    try {
      const res = await uploadFileDetail({ file: file.raw })
      formData.modelFileId = res.data.id.toString()
      ElMessage.success('模型文件上传成功')
    } catch (error) {
      ElMessage.error('模型文件上传失败')
      console.error('模型文件上传失败:', error)
    }
  }
}

const fillWithTestData = () => {
  formData.modelName = '测试模型 ' + new Date().getTime()
  formData.modelFileId = '12345'
  formData.warningType = 'reflective_vest'
}

defineExpose({
  open
})
</script>

<style scoped>
.upload-tip {
  font-size: 14px;
  color: #999;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
