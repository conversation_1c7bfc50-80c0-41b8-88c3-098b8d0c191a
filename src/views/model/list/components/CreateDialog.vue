<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加模型"
    width="500px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="模型名称" prop="modelName">
        <el-input
          v-model="formData.modelName"
          placeholder="请输入模型名称"
          clearable
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="告警类型" prop="warningType">
        <el-input
          v-model="formData.warningType"
          placeholder="请输入告警类型"
          clearable
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="上传封面图" prop="coverImageFileId">
        <UploadImgDetail
          v-model="coverUrl"
          :file-size="5"
          :file-type="['image/jpeg', 'image/png']"
          width="120px"
          height="120px"
          :upload-api="handleUpload"
          @upload-success="handleUploadSuccess"
        />
      </el-form-item>

      <el-form-item label="上传模型" prop="modelFileId">
        <div class="model-upload-container">
          <input
            ref="modelFileInputRef"
            type="file"
            accept=".pt"
            style="display: none"
            @change="handleModelFileSelect"
          />
          <div class="upload-button-wrapper">
            <el-button
              type="primary"
              @click="selectModelFile"
              class="select-file-btn"
              :disabled="isUploading"
              :loading="isUploading"
            >
              <el-icon v-if="!isUploading"><Upload /></el-icon>
              {{ isUploading ? '处理中...' : '选择模型文件' }}
            </el-button>
          </div>

          <!-- 上传进度显示 -->
          <div v-if="modelUploadProgress > 0" class="upload-progress">
            <el-progress :percentage="modelUploadProgress" :status="modelUploadStatus" />
          </div>

          <!-- 已选择的文件信息 -->
          <div v-if="selectedModelFile" class="selected-file">
            <span class="file-name">{{ selectedModelFile.name }}</span>
            <span class="file-size">({{ formatFileSize(selectedModelFile.size) }})</span>
          </div>

          <div class="upload-tip"> 请上传 .pt 格式的模型文件，最大 1GB </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="fillWithTestData">快速填充</el-button>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { UploadImgDetail } from '@/components/UploadFile'
import { Upload } from '@element-plus/icons-vue'
import { createModel } from '@/api/alg/model'
import { uploadFileDetail } from '@/api/infra/file'
import { resumableUploadService } from '@/services/resumableUploadService'
import type { CreateModelForm } from '../types'

defineOptions({ name: 'CreateModelDialog' })

const emit = defineEmits<{
  success: []
}>()

const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()
const modelFileInputRef = ref<HTMLInputElement>()

const formData = reactive<CreateModelForm>({
  modelName: '',
  modelFileId: '',
  coverImageFileId: undefined,
  modelVideoFileId: undefined,
  status: undefined,
  warningType: undefined
})
const coverUrl = ref('')
let coverImageFileId: number | undefined

// 模型文件上传相关状态
const selectedModelFile = ref<File | null>(null)
const modelUploadProgress = ref(0)
const modelUploadStatus = ref<'success' | 'exception' | undefined>(undefined)
const isUploading = ref(false)

const formRules: FormRules<CreateModelForm> = {
  modelName: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { min: 1, max: 50, message: '模型名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  modelFileId: [{ required: true, message: '请上传模型文件', trigger: 'blur' }],
  warningType: [{ required: true, message: '请选择告警类型', trigger: 'change' }],
  coverImageFileId: [{ required: true, message: '请上传封面图', trigger: 'change' }]
}

/** 打开弹窗 */
const open = () => {
  dialogVisible.value = true
  resetForm()
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  Object.assign(formData, {
    modelName: '',
    modelFileId: '',
    coverImageFileId: undefined,
    modelVideoFileId: undefined,
    status: undefined,
    warningType: undefined
  })
  coverUrl.value = ''
  coverImageFileId = undefined

  // 重置模型文件上传状态
  selectedModelFile.value = null
  modelUploadProgress.value = 0
  modelUploadStatus.value = undefined

  // 清理文件输入框
  if (modelFileInputRef.value) {
    modelFileInputRef.value.value = ''
  }

  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/** 提交表单 */
const handleSubmit = async () => {
  if (!formRef.value) return

  const isValid = await formRef.value.validate().catch(() => false)
  if (!isValid) return

  loading.value = true
  try {
    const payload = {
      ...formData,
      ...(coverImageFileId && { coverImageFileId })
    }

    await createModel(payload)
    ElMessage.success('创建成功')
    handleClose()
    emit('success')
  } catch (error) {
    ElMessage.error('创建失败')
    console.error('创建模型失败:', error)
  } finally {
    loading.value = false
  }
}

const handleUpload = async (file: File) => {
  const res = await uploadFileDetail({ file })
  ElMessage.success('上传成功')
  return res
}

const handleUploadSuccess = (res: any) => {
  coverUrl.value = res.data.url
  coverImageFileId = res.data.id
  formData.coverImageFileId = res.data.id // 同时更新表单数据
}

/** 选择模型文件 */
const selectModelFile = () => {
  modelFileInputRef.value?.click()
}

/** 模型文件选择处理 */
const handleModelFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // 立即显示文件选择状态
  selectedModelFile.value = file
  modelUploadProgress.value = 0
  modelUploadStatus.value = undefined
  isUploading.value = true

  // 文件类型检查 - 只支持 .pt 格式
  const isValidType = file.name.toLowerCase().endsWith('.pt')

  // 文件大小检查（最大 1GB）
  const isLt1G = file.size / 1024 / 1024 / 1024 < 1

  if (!isValidType) {
    ElMessage.error('请上传 .pt 格式的模型文件！')
    resetModelUploadState()
    return
  }
  if (!isLt1G) {
    ElMessage.error('模型文件大小不能超过 1GB！')
    resetModelUploadState()
    return
  }

  // 文件验证通过，准备上传

  // 使用 setTimeout 让用户看到状态变化
  setTimeout(() => {
    startModelUpload(file)
  }, 500)
}

/** 重置模型上传状态 */
const resetModelUploadState = () => {
  selectedModelFile.value = null
  modelUploadProgress.value = 0
  modelUploadStatus.value = undefined
  isUploading.value = false
  if (modelFileInputRef.value) {
    modelFileInputRef.value.value = ''
  }
}

/** 开始模型文件上传 */
const startModelUpload = async (file: File) => {
  try {
    modelUploadProgress.value = 0
    modelUploadStatus.value = undefined

    await resumableUploadService.startUpload(
      file,
      undefined, // 模型上传不需要 datasetId
      (progress) => {
        modelUploadProgress.value = Math.round(progress)
      },
      (_, fileInfo) => {
        modelUploadProgress.value = 100
        modelUploadStatus.value = 'success'
        isUploading.value = false
        formData.modelFileId = fileInfo.id.toString()
        // 移除重复的成功提示，resumableUploadService 内部已经有提示了
      },
      (error) => {
        modelUploadStatus.value = 'exception'
        isUploading.value = false
        ElMessage.error(`模型文件上传失败: ${error}`)
      }
    )
  } catch (error) {
    modelUploadStatus.value = 'exception'
    isUploading.value = false
    ElMessage.error('模型文件上传失败')
    console.error('模型文件上传失败:', error)
  }
}

/** 格式化文件大小 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const fillWithTestData = () => {
  formData.modelName = '测试模型 ' + new Date().getTime()
  formData.modelFileId = '12345'
  formData.warningType = 'crack'
}

defineExpose({
  open
})
</script>

<style scoped>
.model-upload-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.upload-button-wrapper {
  display: flex;
  justify-content: flex-start;
}

.select-file-btn {
  width: auto !important;
  min-width: 140px;
}

.upload-progress {
  margin-top: 8px;
}

.selected-file {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
}

.file-name {
  color: #303133;
  font-weight: 500;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}
</style>
