<template>
  <div class="dataset-detail-container app-container">
    <!-- Breadcrumb -->
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/dataset' }">数据集</el-breadcrumb-item>
      <el-breadcrumb-item>数据集明细</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- Dataset Info -->
    <InfoCard :dataset="dataset" />

    <!-- File List -->
    <FileManager
      :query-params="queryParams"
      :total="total"
      :file-images="fileImages"
      :dataset-id="datasetId"
      @upload="handleUpload"
      @page-change="handlePageChange"
      @delete="handleDelete"
      @upload-success="handleUploadSuccess"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as DatasetApi from '@/api/alg/dataset'
import type { Dataset } from '../list/types'
import InfoCard from './components/InfoCard.vue'
import FileManager from './components/FileManager.vue'

defineOptions({ name: 'DatasetDetail' })

const route = useRoute()
const datasetId = route.query.id as string
const dataset = ref<Dataset>()

const queryParams = reactive({
  pageNo: 1,
  pageSize: 20
})
const total = ref(0)
const fileImages = ref([
  // 添加一些测试图片数据，方便测试删除功能
  'https://picsum.photos/256/160?random=1',
  'https://picsum.photos/256/160?random=2',
  'https://picsum.photos/256/160?random=3',
  'https://picsum.photos/256/160?random=4',
  'https://picsum.photos/256/160?random=5',
  'https://picsum.photos/256/160?random=6'
])

const getDatasetDetail = async () => {
  if (datasetId) {
    const data = await DatasetApi.getDataset({ id: datasetId })
    dataset.value = data
    // TODO: fetch file list
  }
}

const handleUpload = () => {
  // TODO: 实现文件上传逻辑
  console.log('上传文件')
}

const handleDelete = (fileIndexes: number[]) => {
  // TODO: 实现文件删除逻辑
  console.log('删除文件:', fileIndexes)
  // 这里应该调用删除API，然后重新获取文件列表
  // 暂时从本地数组中移除对应的文件
  const newFileImages = fileImages.value.filter((_, index) => !fileIndexes.includes(index))
  fileImages.value = newFileImages
}

const handleUploadSuccess = (fileUrl: string) => {
  // 上传成功后，将新文件添加到文件列表
  console.log('文件上传成功:', fileUrl)
  fileImages.value.push(fileUrl)
  // TODO: 这里可以调用API将文件信息保存到数据集中
}

const handleRefresh = async () => {
  // 刷新数据集详情，重新获取文件列表
  console.log('刷新数据集详情')
  try {
    const response = await DatasetApi.getDataset({ id: datasetId })
    const datasetDetail = response.data || response

    // 更新数据集信息
    dataset.value = datasetDetail

    // 更新文件列表
    if (datasetDetail.datasetFiles) {
      fileImages.value = datasetDetail.datasetFiles.map((file: any) => file.fileUrl)
      total.value = datasetDetail.datasetFiles.length
    }

    console.log('数据集详情刷新成功')
  } catch (error) {
    console.error('刷新数据集详情失败:', error)
    ElMessage.error('刷新失败')
  }
}

const handlePageChange = (params: { pageNo: number; pageSize: number }) => {
  queryParams.pageNo = params.pageNo
  queryParams.pageSize = params.pageSize
  // TODO: 重新获取文件列表
  console.log('分页变化:', params)
}

onMounted(() => {
  getDatasetDetail()
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 74px);
  gap: 12px;
}
</style>
