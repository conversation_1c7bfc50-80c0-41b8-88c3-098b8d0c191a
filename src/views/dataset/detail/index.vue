<template>
  <div class="dataset-detail-container app-container">
    <!-- Breadcrumb -->
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/dataset' }">数据集</el-breadcrumb-item>
      <el-breadcrumb-item>数据集明细</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- Dataset Info -->
    <InfoCard :dataset="dataset" />

    <!-- File List -->
    <FileManager
      :query-params="queryParams"
      :total="total"
      :dataset-files="datasetFiles"
      :dataset-id="datasetId"
      @upload="handleUpload"
      @page-change="handlePageChange"
      @delete="handleDelete"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as DatasetApi from '@/api/alg/dataset'
import type { Dataset, DatasetFile } from '../list/types'
import InfoCard from './components/InfoCard.vue'
import FileManager from './components/FileManager.vue'

defineOptions({ name: 'DatasetDetail' })

const route = useRoute()
const datasetId = route.query.id as string
const dataset = ref<Dataset>()

const queryParams = reactive({
  pageNo: 1,
  pageSize: 20
})
const total = ref(0)
const datasetFiles = ref<DatasetFile[]>([])

const getDatasetDetail = async () => {
  if (datasetId) {
    try {
      const response = await DatasetApi.getDataset({ id: datasetId })
      // 解析接口返回的数据结构
      dataset.value = response.data || response
      await getFileList()
    } catch (error) {
      console.error('获取数据集详情失败:', error)
      ElMessage.error('获取数据集详情失败')
    }
  }
}

const getFileList = async () => {
  if (!datasetId) return

  try {
    const response = await DatasetApi.getDatasetFilesPage({
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
      datasetId: Number(datasetId)
    })

    // 解析接口返回的数据结构
    const data = response.data || response
    datasetFiles.value = data.list || []
    total.value = data.total || 0
  } catch (error) {
    console.error('获取文件列表失败:', error)
    ElMessage.error('获取文件列表失败')
  }
}

const handleUpload = () => {
  // TODO: 实现文件上传逻辑
  console.log('上传文件')
}

const handleDelete = async (fileIndexes: number[]) => {
  try {
    // 根据索引获取要删除的文件ID
    const fileIdsToDelete = fileIndexes
      .map((index) => datasetFiles.value[index]?.id)
      .filter(Boolean)

    if (fileIdsToDelete.length === 0) {
      ElMessage.warning('没有选中要删除的文件')
      return
    }

    // 调用删除API
    await DatasetApi.deleteDatasetFilesList(fileIdsToDelete)
    ElMessage.success('删除成功')

    // 重新获取文件列表
    await getFileList()
  } catch (error) {
    console.error('删除文件失败:', error)
    ElMessage.error('删除文件失败')
  }
}

const handleRefresh = async () => {
  console.log('刷新数据集详情')
  try {
    // 重新获取数据集详情和文件列表
    await getDatasetDetail()
    console.log('数据集详情刷新成功')
  } catch (error) {
    console.error('刷新数据集详情失败:', error)
    ElMessage.error('刷新失败')
  }
}

const handlePageChange = async (params: { pageNo: number; pageSize: number }) => {
  queryParams.pageNo = params.pageNo
  queryParams.pageSize = params.pageSize
  await getFileList()
}

onMounted(() => {
  getDatasetDetail()
})
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 74px);
  gap: 12px;
}
</style>
